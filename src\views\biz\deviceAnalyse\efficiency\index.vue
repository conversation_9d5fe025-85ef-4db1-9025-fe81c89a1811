<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deviceTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <!--能耗数据-->
      <el-col :span="20" :xs="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #front>
            <el-form-item label="点位类型" prop="pointType">
              <el-select
                v-model="queryParams.pointType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.point_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #actions>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </template>
        </time-analysis-selector>

        <!--   图表数据    -->


      </el-col>

    </el-row>
  </div>
</template>

<script>

import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {deviceTree} from "@/api/biz/device";

export default {
  name: 'energyEfficiencyAnalyse',
  components: {TimeAnalysisSelector},
  dicts: ['point_type'],
  data() {
    return {
      deviceTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,
      energyType: '1',

      // 查询参数
      queryParams: {
        deviceIds: [],
        analysisType: undefined,
        pointType: undefined,
        startTime: undefined,
        endTime: undefined
      },
      // 遮罩层
      loading: false,

    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDeviceTree()
  },
  methods: {

    // 获取区域表具树结构
    getDeviceTree() {
      this.typeLoading = true
      deviceTree().then(res => {
        this.deviceTreeOptions = res.data
        this.typeLoading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.deviceIds = []

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.getEnergyAnalyse()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.analysisType || !this.queryParams.startTime ||
        !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择设备、点位、分析方式和时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "device" 的节点 ID
      this.queryParams.deviceIds = checkedNodes
        .filter(node => node.type === 'device')
        .map(node => node.id);

      // 检查是否选择了表具
      if (this.queryParams.deviceIds.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return false;
      }
      return true;
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType
      this.queryParams.startTime = params.startTime
      this.queryParams.endTime = params.endTime
    },
  }
}
</script>

<style scoped>

</style>
