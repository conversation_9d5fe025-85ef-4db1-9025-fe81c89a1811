<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deviceTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <!--能耗数据-->
      <el-col :span="20" :xs="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #front>
            <el-form-item label="点位类型" prop="pointType">
              <el-select
                v-model="queryParams.pointType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.point_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #actions>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </template>
        </time-analysis-selector>

        <!--   图表数据    -->
        <el-row :gutter="20">
          <!-- COP趋势曲线图 -->
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">COP趋势分析</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('trend')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('copTrendChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="copTrendChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- COP统计对比图 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">COP统计对比</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('compare')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('copCompareChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="copCompareChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>

          <!-- COP分布饼图 -->
          <el-col :span="12">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">COP分布占比</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('distribution')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('copDistributionChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="copDistributionChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 数据视图对话框 -->
        <el-dialog :title="dataViewTitle" :visible.sync="dataViewVisible" width="60%">
          <!-- COP趋势数据视图 -->
          <el-table v-if="dataViewType === 'trend'" :data="dataViewData" border style="width: 100%">
            <el-table-column prop="date" label="日期" width="120"></el-table-column>
            <el-table-column v-for="device in analyseData" :key="device.deviceId" :label="device.deviceName">
              <template slot-scope="scope">
                {{ scope.row.devices[device.deviceId] ? scope.row.devices[device.deviceId].value.toFixed(2) : '0.00' }}
              </template>
            </el-table-column>
          </el-table>

          <!-- COP统计对比数据视图 -->
          <el-table v-else-if="dataViewType === 'compare'" :data="dataViewData" border style="width: 100%">
            <el-table-column prop="deviceName" label="设备名称"></el-table-column>
            <el-table-column prop="avgCop" label="平均COP">
              <template slot-scope="scope">
                {{ scope.row.avgCop.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="optimalCop" label="最佳COP">
              <template slot-scope="scope">
                {{ scope.row.optimalCop.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="worstCop" label="最差COP">
              <template slot-scope="scope">
                {{ scope.row.worstCop.toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- COP分布数据视图 -->
          <el-table v-else :data="dataViewData" border style="width: 100%">
            <el-table-column prop="deviceName" label="设备名称"></el-table-column>
            <el-table-column prop="avgCop" label="平均COP">
              <template slot-scope="scope">
                {{ scope.row.avgCop.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="占比">
              <template slot-scope="scope">
                {{ scope.row.percentage.toFixed(1) }}%
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>


      </el-col>

    </el-row>
  </div>
</template>

<script>

import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {deviceTree} from "@/api/biz/device";
import {efficiencyAnalyse} from "@/api/biz/deviceAnalyse";
import * as echarts from 'echarts';

export default {
  name: 'energyEfficiencyAnalyse',
  components: {TimeAnalysisSelector},
  dicts: ['point_type'],
  data() {
    return {
      deviceTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,
      energyType: '1',

      // 查询参数
      queryParams: {
        deviceIds: [],
        analysisType: undefined,
        pointType: undefined,
        startTime: undefined,
        endTime: undefined
      },
      // 遮罩层
      loading: false,

      // 图表实例
      copTrendChart: null,
      copCompareChart: null,
      copDistributionChart: null,

      // 分析数据
      analyseData: [],

      // 数据视图
      dataViewVisible: false,
      dataViewTitle: 'COP数据详情',
      dataViewData: [],
      dataViewType: 'trend', // 'trend', 'compare', 'distribution'

      // 图表颜色
      chartColors: [
        '#5470C6', '#91CC75', '#FAC858', '#EE6666',
        '#73C0DE', '#3BA272', '#FC8452', '#9A60B4',
        '#EA7CCC', '#FF9F7F', '#FFDB5C', '#9FE6B8'
      ]
    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDeviceTree()
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.resizeCharts)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    // 能效分析
    getEfficiencyAnalyse(){
      this.loading = true
      efficiencyAnalyse(this.queryParams).then(res => {
        this.analyseData = res.data || []
        this.loading = false
        this.initCharts()
        this.prepareDataViewData()
      }).catch(error => {
        console.error('获取能效分析数据失败:', error)
        this.$message.error('获取能效分析数据失败')
        this.loading = false
      })
    },

    // 初始化所有图表
    initCharts() {
      this.initCopTrendChart()
      this.initCopCompareChart()
      this.initCopDistributionChart()
    },

    // 初始化COP趋势曲线图
    initCopTrendChart() {
      // 销毁旧图表
      if (this.copTrendChart) {
        this.copTrendChart.dispose()
      }

      const chartDom = this.$refs.copTrendChart
      if (!chartDom) return

      this.copTrendChart = echarts.init(chartDom, 'macarons')

      // 准备数据
      const timeData = this.prepareTrendData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              result += `<div style="margin: 3px 0">
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: ${param.value.toFixed(2)}
              </div>`
            }

            return result
          }
        },
        legend: {
          data: timeData.legendData,
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        dataZoom: [],
        xAxis: {
          type: 'category',
          data: timeData.times,
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理
              if (!value) return '';

              try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                  return value;
                }

                if (value.includes('-') || value.includes('/')) {
                  if (value.includes(':') || value.includes(' ')) {
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
                  } else if (value.length <= 7) {
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                  } else {
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }
                } else if (value.length === 4) {
                  return value;
                } else {
                  return value;
                }
              } catch (e) {
                console.error('Date formatting error:', e);
                return value;
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: 'COP值',
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: timeData.series
      }

      this.copTrendChart.setOption(option)
    },

    // 初始化COP统计对比图
    initCopCompareChart() {
      // 销毁旧图表
      if (this.copCompareChart) {
        this.copCompareChart.dispose()
      }

      const chartDom = this.$refs.copCompareChart
      if (!chartDom) return

      this.copCompareChart = echarts.init(chartDom, 'macarons')

      // 准备数据
      const compareData = this.prepareCompareData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              result += `<div style="margin-top:5px">
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: ${param.value.toFixed(2)}
              </div>`
            }

            return result
          }
        },
        legend: {
          data: ['平均COP', '最佳COP', '最差COP'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        dataZoom: [],
        xAxis: {
          type: 'category',
          data: compareData.deviceNames,
          boundaryGap: true,
          axisLabel: {
            interval: 'auto',
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: 'COP值',
          axisLabel: {
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: compareData.series
      }

      this.copCompareChart.setOption(option)
    },

    // 初始化COP分布饼图
    initCopDistributionChart() {
      // 销毁旧图表
      if (this.copDistributionChart) {
        this.copDistributionChart.dispose()
      }

      const chartDom = this.$refs.copDistributionChart
      if (!chartDom) return

      this.copDistributionChart = echarts.init(chartDom, 'macarons')

      // 准备数据
      const distributionData = this.prepareDistributionData()

      // 设置图表选项
      const option = {
        title: {
          text: '',
          subtext: '',
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            return `<div style="font-weight:bold">${params.name}</div>
                   <div style="margin-top:5px">
                     <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params.color}"></span>
                     平均COP: ${params.value.toFixed(2)}
                   </div>
                   <div style="margin-top:3px">
                     占比: ${params.percent}%
                   </div>`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: distributionData.legendData
        },
        series: [
          {
            name: 'COP分布',
            type: 'pie',
            radius: '50%',
            data: distributionData.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              formatter: '{b}: {c}\n({d}%)'
            }
          }
        ]
      }

      this.copDistributionChart.setOption(option)
    },

    // 准备趋势数据
    prepareTrendData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { times: [], legendData: [], series: [] }
      }

      // 使用第一个设备的时间作为X轴
      const times = this.analyseData[0].analyseData.times || []
      const legendData = this.analyseData.map(item => item.deviceName)

      // 为每个设备创建一个系列
      const series = this.analyseData.map((device, index) => {
        const color = this.chartColors[index % this.chartColors.length]
        return {
          name: device.deviceName,
          type: 'line',
          data: device.analyseData.values.map(val => parseFloat(val.toFixed(2))),
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: color
          },
          itemStyle: {
            color: color,
            borderWidth: 2,
            borderColor: '#fff'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: color,
              shadowOffsetY: 0,
              borderWidth: 3
            },
            lineStyle: {
              width: 4
            }
          },
          areaStyle: {
            opacity: 0.1,
            color: color
          }
        }
      })

      return { times, legendData, series }
    },

    // 准备对比数据
    prepareCompareData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { deviceNames: [], series: [] }
      }

      const deviceNames = this.analyseData.map(item => item.deviceName)
      const avgCopData = this.analyseData.map(item => parseFloat(item.avgCop.toFixed(2)))
      const optimalCopData = this.analyseData.map(item => parseFloat(item.optimalCop.toFixed(2)))
      const worstCopData = this.analyseData.map(item => parseFloat(item.worstCop.toFixed(2)))

      const series = [
        {
          name: '平均COP',
          type: 'bar',
          data: avgCopData,
          itemStyle: {
            color: '#5470C6'
          }
        },
        {
          name: '最佳COP',
          type: 'bar',
          data: optimalCopData,
          itemStyle: {
            color: '#91CC75'
          }
        },
        {
          name: '最差COP',
          type: 'bar',
          data: worstCopData,
          itemStyle: {
            color: '#EE6666'
          }
        }
      ]

      return { deviceNames, series }
    },

    // 准备分布数据
    prepareDistributionData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        return { legendData: [], data: [] }
      }

      // 计算总的平均COP
      const totalAvgCop = this.analyseData.reduce((sum, item) => sum + item.avgCop, 0)

      const legendData = this.analyseData.map(item => item.deviceName)
      const data = this.analyseData.map((item, index) => {
        const percentage = totalAvgCop > 0 ? (item.avgCop / totalAvgCop * 100) : 0
        return {
          name: item.deviceName,
          value: parseFloat(item.avgCop.toFixed(2)),
          percentage: parseFloat(percentage.toFixed(1)),
          itemStyle: {
            color: this.chartColors[index % this.chartColors.length]
          }
        }
      })

      return { legendData, data }
    },

    // 准备数据视图数据
    prepareDataViewData() {
      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      // 默认准备趋势数据视图
      this.prepareTrendDataView()
    },

    // 准备趋势数据视图
    prepareTrendDataView() {
      this.dataViewType = 'trend'
      this.dataViewTitle = 'COP趋势数据详情'

      const firstDevice = this.analyseData[0]
      if (!firstDevice) {
        this.dataViewData = []
        return
      }

      const times = firstDevice.analyseData.times || []

      // 为每个时间点创建一行数据
      this.dataViewData = times.map((time, timeIndex) => {
        const rowData = {
          date: time,
          devices: {}
        }

        // 添加每个设备在该时间点的COP值
        this.analyseData.forEach(device => {
          const value = device.analyseData.values[timeIndex] || 0
          rowData.devices[device.deviceId] = {
            deviceName: device.deviceName,
            value: parseFloat(value.toFixed(2))
          }
        })

        return rowData
      })
    },

    // 准备对比数据视图
    prepareCompareDataView() {
      this.dataViewType = 'compare'
      this.dataViewTitle = 'COP统计对比详情'

      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      this.dataViewData = this.analyseData.map(device => {
        return {
          deviceName: device.deviceName,
          deviceId: device.deviceId,
          avgCop: parseFloat(device.avgCop.toFixed(2)),
          optimalCop: parseFloat(device.optimalCop.toFixed(2)),
          worstCop: parseFloat(device.worstCop.toFixed(2))
        }
      })
    },

    // 准备分布数据视图
    prepareDistributionDataView() {
      this.dataViewType = 'distribution'
      this.dataViewTitle = 'COP分布详情'

      if (!this.analyseData || this.analyseData.length === 0) {
        this.dataViewData = []
        return
      }

      // 计算总的平均COP
      const totalAvgCop = this.analyseData.reduce((sum, item) => sum + item.avgCop, 0)

      this.dataViewData = this.analyseData.map(device => {
        const percentage = totalAvgCop > 0 ? (device.avgCop / totalAvgCop * 100) : 0
        return {
          deviceName: device.deviceName,
          deviceId: device.deviceId,
          avgCop: parseFloat(device.avgCop.toFixed(2)),
          percentage: parseFloat(percentage.toFixed(1))
        }
      })
    },

    // 显示数据视图
    showDataView(type) {
      switch (type) {
        case 'trend':
          this.prepareTrendDataView()
          break
        case 'compare':
          this.prepareCompareDataView()
          break
        case 'distribution':
          this.prepareDistributionDataView()
          break
        default:
          this.prepareTrendDataView()
      }
      this.dataViewVisible = true
    },

    // 保存图表为图片
    saveAsImage(chartRef) {
      const chart = this[chartRef]
      if (!chart) return

      const url = chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.download = 'COP分析_' + new Date().getTime() + '.png'
      link.href = url
      link.click()
    },

    // 调整图表大小
    resizeCharts() {
      if (this.copTrendChart) {
        this.copTrendChart.resize()
      }
      if (this.copCompareChart) {
        this.copCompareChart.resize()
      }
      if (this.copDistributionChart) {
        this.copDistributionChart.resize()
      }
    },

    // 销毁图表
    disposeCharts() {
      if (this.copTrendChart) {
        this.copTrendChart.dispose()
        this.copTrendChart = null
      }
      if (this.copCompareChart) {
        this.copCompareChart.dispose()
        this.copCompareChart = null
      }
      if (this.copDistributionChart) {
        this.copDistributionChart.dispose()
        this.copDistributionChart = null
      }
    },
    // 获取区域设备树结构
    getDeviceTree() {
      this.typeLoading = true
      deviceTree().then(res => {
        this.deviceTreeOptions = res.data
        this.typeLoading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.deviceIds = []

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.getEfficiencyAnalyse()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.analysisType || !this.queryParams.startTime ||
        !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择设备、点位、分析方式和时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "device" 的节点 ID
      this.queryParams.deviceIds = checkedNodes
        .filter(node => node.type === 'device')
        .map(node => node.id);

      // 检查是否选择了设备
      if (this.queryParams.deviceIds.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return false;
      }
      return true;
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType
      this.queryParams.startTime = params.startTime
      this.queryParams.endTime = params.endTime
    },
  }
}
</script>

<style scoped>

</style>
